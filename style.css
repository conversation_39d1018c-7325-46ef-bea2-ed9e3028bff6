/* --- Theme and General Styles --- */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500;600&family=Barlow:wght@400;500;600;700&display=swap');

:root {
  /* Primary Colors - Military-inspired palette */
  --primary-color: #1d3557; /* Deep navy blue - more military feel */
  --primary-light: #2a4a73; /* Lighter navy */
  --primary-dark: #0f1e33; /* Darker navy */

  /* Secondary Colors */
  --secondary-color: #455a64; /* Steel blue-gray */
  --secondary-light: #607d8b; /* Lighter steel */
  --secondary-dark: #263238; /* Darker steel */

  /* Accent Colors */
  --accent-color: #2e7d32; /* Forest green */
  --accent-light: #388e3c; /* Lighter forest */
  --accent-dark: #1b5e20; /* Darker forest */

  /* Feedback Colors */
  --danger-color: #b71c1c; /* Deep red */
  --danger-light: #c62828; /* Lighter deep red */
  --danger-dark: #7f0000; /* Darker deep red */

  --info-color: #0d47a1; /* Deep blue */
  --info-light: #1565c0; /* Lighter deep blue */
  --info-dark: #002171; /* Darker deep blue */

  --action-color: #e65100; /* Deep orange */
  --action-light: #ef6c00; /* Lighter deep orange */
  --action-dark: #ac1900; /* Darker deep orange */
  --action-color-rgb: 230, 81, 0; /* RGB values for action color */

  --success-color: #2e7d32; /* Forest green */
  --warning-color: #e65100; /* Deep amber */

  /* Background Colors */
  --background-light: #eef2f7; /* Very light gray with subtle blue tint */
  --background-medium: #dde4eb; /* Light gray with subtle blue tint */
  --surface-color: #ffffff; /* White for cards/containers */

  /* Military-inspired accent colors */
  --military-olive: #4b5320;
  --military-olive-light: #5c6627;
  --military-olive-dark: #3a4119;
  --military-tan: #c6b793;
  --military-brown: #5d4037;
  --military-gray: #546e7a;

  /* Text Colors */
  --text-dark: #1a2530; /* Very dark blue-gray */
  --text-medium: #37474f; /* Medium blue-gray */
  --text-light: #ffffff; /* White */
  --text-muted: #607d8b; /* Muted text */

  /* Border Colors */
  --border-color: #cfd8dc; /* Light blue-gray border */
  --border-color-hover: #b0bec5; /* Slightly darker on hover */
  --border-accent: #4b5320; /* Military olive for accents */

  /* UI Properties */
  --border-radius-sm: 4px;
  --border-radius: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;

  /* Typography */
  --font-family-ui: 'Barlow', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, sans-serif; /* Professional military-style UI font */
  --font-family-data: 'Roboto Mono', 'Consolas', 'Courier New', monospace; /* Monospaced font for data */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing */
  --spacing-unit: 8px; /* Base spacing unit */

  /* Shadows */
  --box-shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.12);
  --box-shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.07);
  --box-shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.12), 0 4px 6px -2px rgba(0, 0, 0, 0.07);
  --box-shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.06);

  /* Transitions */
  --transition-speed: 0.2s;
  --transition-function: cubic-bezier(0.4, 0, 0.2, 1);

  /* Military-inspired patterns */
  --grid-pattern: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h20v20H0V0zm10 10h10v10H10V10zM0 10h10v10H0V10z' fill='%23000000' fill-opacity='0.03'/%3E%3C/svg%3E");
  --topo-pattern: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0c50 0 50 20 100 20V0H0z' fill='%23000000' fill-opacity='0.02'/%3E%3Cpath d='M0 20c50 0 50 20 100 20V20H0z' fill='%23000000' fill-opacity='0.02'/%3E%3Cpath d='M0 40c50 0 50 20 100 20V40H0z' fill='%23000000' fill-opacity='0.02'/%3E%3Cpath d='M0 60c50 0 50 20 100 20V60H0z' fill='%23000000' fill-opacity='0.02'/%3E%3Cpath d='M0 80c50 0 50 20 100 20V80H0z' fill='%23000000' fill-opacity='0.02'/%3E%3C/svg%3E");
}

body {
  font-family: var(--font-family-ui);
  background-color: var(--background-light);
  background-image: var(--topo-pattern);
  color: var(--text-medium);
  margin: 0;
  padding: calc(var(--spacing-unit) * 4); /* 32px padding */
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: var(--font-weight-regular);
  letter-spacing: 0.01em;
  position: relative;
  min-height: 100vh;
}

.container {
  width: 95%;
  max-width: 1400px; /* Increased from 1200px for better use of widescreen displays */
  margin-bottom: calc(var(--spacing-unit) * 4); /* Add space at bottom */
  position: relative;
  background-color: rgba(255, 255, 255, 0.92);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  padding: calc(var(--spacing-unit) * 3);
  border: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

/* Enhanced Typography System */
h1, h2, h3, h4, h5, h6 {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin below headings */
  position: relative;
  line-height: 1.25;
  letter-spacing: -0.02em;
  margin-top: 0;
}

h1, h2 {
  text-align: center;
}

/* Main Application Title - Enhanced */
h1 {
  font-size: 2.75rem; /* Slightly larger for more presence */
  font-weight: 700; /* Bolder for main title */
  margin-top: calc(var(--spacing-unit) * 2);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.03em;
  line-height: 1.2;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--military-olive-dark), var(--military-olive-light));
  border-radius: 2px;
}

/* Section Headers - Enhanced */
h2 {
  font-size: 2rem;
  font-weight: 650;
  color: var(--primary-dark);
  padding-bottom: calc(var(--spacing-unit) * 1.5);
  position: relative;
  letter-spacing: -0.025em;
  margin-bottom: calc(var(--spacing-unit) * 3.5);
}

h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--military-olive-dark), var(--military-olive-light));
  border-radius: 2px;
  opacity: 0.9;
}

/* Panel Headers - Enhanced */
h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--secondary-dark);
  padding-left: calc(var(--spacing-unit) * 1.5);
  border-left: 4px solid var(--military-olive-light);
  letter-spacing: -0.02em;
  margin-bottom: calc(var(--spacing-unit) * 2.5);
  position: relative;
}

/* Sub-section Headers - Enhanced */
h4 {
  font-size: 1.25rem;
  color: var(--secondary-dark);
  font-weight: 600;
  letter-spacing: -0.015em;
  margin-bottom: calc(var(--spacing-unit) * 2);
}

/* Small Headers - Enhanced */
h5 {
  font-size: 1.125rem;
  color: var(--secondary-color);
  font-weight: 600;
  letter-spacing: -0.01em;
  margin-bottom: calc(var(--spacing-unit) * 1.5);
}

/* Micro Headers - Enhanced */
h6 {
  font-size: 0.875rem;
  color: var(--text-medium);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  margin-bottom: calc(var(--spacing-unit) * 1);
}

/* Enhanced Intro Text */
.intro {
  text-align: center;
  color: var(--text-medium);
  margin-bottom: calc(var(--spacing-unit) * 4); /* 32px margin below intro */
  font-size: 1.125rem;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  padding: calc(var(--spacing-unit) * 3);
  font-weight: 400;
  letter-spacing: 0.01em;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid var(--military-olive-light);
  box-shadow: var(--box-shadow-subtle);
  position: relative;
}

.intro::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, var(--military-olive-light), transparent);
  opacity: 0.5;
}

/* --- Collapsible Section Styles --- */
.collapsible {
  background-color: var(--background-medium);
  color: var(--text-dark);
  cursor: pointer;
  padding: calc(var(--spacing-unit) * 2.5); /* 20px padding */
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 1.1rem;
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-speed) var(--transition-function);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--spacing-unit) * 2); /* 16px margin */
  box-shadow: var(--box-shadow-subtle);
  position: relative;
  overflow: hidden;
  border-left: 4px solid var(--military-olive);
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
}

.collapsible::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(to right, rgba(var(--military-olive-dark), 0.05), transparent);
  opacity: 0.7;
  transition: opacity var(--transition-speed) var(--transition-function);
}

.collapsible::after {
  content: '\002B'; /* Unicode plus sign */
  color: var(--military-olive-dark);
  font-weight: bold;
  float: right;
  margin-left: auto;
  font-size: 1.2rem;
  transition: transform var(--transition-speed) var(--transition-function);
  position: relative;
  z-index: 2;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
}

.active::after {
  content: '\2212'; /* Unicode minus sign */
  transform: rotate(180deg);
  background-color: rgba(255, 255, 255, 0.3);
}

.collapsible:hover {
  background-color: var(--background-medium);
  box-shadow: var(--box-shadow-medium);
  transform: translateY(-2px);
  border-left-color: var(--military-olive-light);
}

.collapsible:hover::before {
  opacity: 0.9;
}

.collapsible.active {
  background-color: var(--military-olive);
  color: var(--text-light);
  margin-bottom: 0; /* Remove margin when active */
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: var(--box-shadow-medium);
  border-left-color: var(--military-olive-dark);
}

.collapsible.active::before {
  opacity: 0.1;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), transparent);
}

.collapsible.active::after {
  color: var(--text-light);
}

.content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-speed) var(--transition-function), padding 0s var(--transition-speed);
  background-color: var(--surface-color);
  background-image: var(--grid-pattern);
  border: 1px solid var(--border-color);
  border-top: none;
  border-left: 4px solid var(--military-olive-dark);
  border-bottom-left-radius: var(--border-radius-lg);
  border-bottom-right-radius: var(--border-radius-lg);
  margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
  box-shadow: var(--box-shadow-medium);
  visibility: hidden;
  opacity: 0;
  transition: max-height var(--transition-speed) var(--transition-function),
              opacity var(--transition-speed) var(--transition-function),
              visibility 0s var(--transition-speed);
  position: relative;
}

.content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
  pointer-events: none;
  opacity: 0.5;
}

.content ol, .content ul {
  padding-left: calc(var(--spacing-unit) * 3); /* 24px padding */
  margin-bottom: calc(var(--spacing-unit) * 2); /* 16px margin */
  position: relative;
}

.content li {
  margin-bottom: var(--spacing-unit); /* 8px margin */
  position: relative;
}

.content li::marker {
  color: var(--military-olive);
  font-weight: var(--font-weight-semibold);
}

/* --- Map and Controls Layout --- */
.map-controls-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: calc(var(--spacing-unit) * 4); /* 32px gap */
  margin-bottom: calc(var(--spacing-unit) * 4); /* 32px margin */
}

/* --- Map Styles --- */
#map-container, #mortar-map-container {
  width: 100%;
}

#map, #mortar-map {
  height: 500px; /* Slightly increased map height */
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

#map::before, #mortar-map::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--military-olive-dark), var(--military-olive-light));
  z-index: 10;
  opacity: 0.8;
}

/* Map Legend Styles */
#map-legend {
  position: relative;
  background-color: rgba(255, 255, 255, 0.92);
  background-image: var(--grid-pattern);
  border-radius: var(--border-radius);
  padding: calc(var(--spacing-unit) * 2);
  margin-top: calc(var(--spacing-unit) * 1.5);
  box-shadow: var(--box-shadow-medium);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--military-olive);
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

#map-legend::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
  pointer-events: none;
  opacity: 0.5;
}

#map-legend.collapsed {
  max-height: 45px;
  opacity: 0.9;
  cursor: pointer;
  border-left: 4px solid var(--military-olive-light);
}

#map-legend.expanded {
  max-height: 500px;
  opacity: 1;
}

#map-legend h4 {
  margin-top: 0;
  margin-bottom: calc(var(--spacing-unit) * 1.5);
  font-size: 1rem;
  color: var(--primary-color);
  text-align: center;
  font-weight: 600;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

#map-legend h4::after {
  content: '▼';
  font-size: 0.75rem;
  margin-left: calc(var(--spacing-unit) * 1);
  transition: all var(--transition-speed) var(--transition-function);
  background-color: var(--military-olive-light);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 1px solid var(--military-olive-dark);
}

#map-legend.collapsed h4::after {
  transform: rotate(-90deg);
  background-color: var(--military-olive);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

#map-legend.collapsed h4 {
  margin-bottom: 0;
}

#map-legend.collapsed {
  background-color: rgba(255, 255, 255, 0.85);
}

#map-legend.collapsed:hover {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: var(--box-shadow-large);
  transform: translateY(-2px);
}

.legend-content {
  transition: opacity 0.3s ease-in-out;
  position: relative;
  z-index: 2;
}

#map-legend.collapsed .legend-content {
  opacity: 0;
  pointer-events: none;
}

#map-legend.expanded .legend-content {
  opacity: 1;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: calc(var(--spacing-unit) * 2);
  margin-bottom: calc(var(--spacing-unit) * 2);
}

.force-type-indicators {
  display: flex;
  justify-content: center;
  gap: calc(var(--spacing-unit) * 3);
  margin-top: calc(var(--spacing-unit) * 2);
  padding-top: calc(var(--spacing-unit) * 2);
  border-top: 1px solid var(--border-color);
  position: relative;
}

.force-type-indicators::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 0;
  width: 80px;
  height: 1px;
  background: linear-gradient(to right, var(--military-olive), transparent);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: calc(var(--spacing-unit) * 1.25);
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.5);
  padding: calc(var(--spacing-unit) * 0.75) calc(var(--spacing-unit) * 1.25);
  border-radius: var(--border-radius-sm);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.legend-item:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-subtle);
}

.legend-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Common military icon style */
.legend-military-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #777;
  background-color: rgba(119, 119, 119, 0.9);
  color: white;
  font-size: 7px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Common styles for legend icons */
.legend-icon {
  width: 20px;
  height: 20px;
  border: 1px solid #555;
  background-color: #777;
}

/* Infantry icon - Circle */
.legend-infantry {
  width: 20px;
  height: 20px;
  border: 1px solid #555;
  background-color: #777;
  border-radius: 50%;
}

/* Tank icon - Triangle */
.legend-tank {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-tank::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 20px solid #777;
  top: 0;
  left: 0;
}

/* MG icon - Square */
.legend-mg {
  width: 20px;
  height: 20px;
  border: 1px solid #555;
  background-color: #777;
}

/* Artillery icon - Diamond */
.legend-artillery {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-artillery::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #777;
  transform: rotate(45deg);
  top: 0;
  left: 0;
  border: 1px solid #555;
}

/* Anti-Aircraft icon - Diamond with cross */
.legend-antiair {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-antiair::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #777;
  transform: rotate(45deg);
  top: 0;
  left: 0;
  border: 1px solid #555;
}

.legend-antiair .cross-horizontal {
  position: absolute;
  top: 9.5px;
  left: 0;
  width: 20px;
  height: 1px;
  background-color: white;
  z-index: 1;
}

.legend-antiair .cross-vertical {
  position: absolute;
  top: 0;
  left: 9.5px;
  width: 1px;
  height: 20px;
  background-color: white;
  z-index: 1;
}

/* Reconnaissance icon - Hexagon */
.legend-recon {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-recon svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
}

/* APC icon - Rounded Rectangle */
.legend-apc {
  width: 24px;
  height: 16px;
  border: 1px solid #555;
  background-color: #777;
  border-radius: 5px;
}

/* Helicopter icon - Pentagon */
.legend-heli {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-heli svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
}

/* Command Post icon - Star */
.legend-command {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-command svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
}

/* Force type indicators */
.legend-friendly {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: blue;
  border: 1px solid #000066;
  display: inline-block;
  margin-right: 8px;
  box-shadow: 0 0 4px rgba(0, 0, 200, 0.4);
  position: relative;
}

.legend-friendly::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

.legend-enemy {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: red;
  border: 1px solid #990000;
  display: inline-block;
  margin-right: 8px;
  box-shadow: 0 0 4px rgba(200, 0, 0, 0.4);
  position: relative;
}

.legend-enemy::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

/* Leaflet Draw Control Styles */
.leaflet-draw-toolbar a {
  transition: all var(--transition-speed) var(--transition-function);
}

/* Grid Instructions Styles */
.grid-instructions {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: calc(var(--spacing-unit) * 2);
  margin-bottom: calc(var(--spacing-unit) * 2);
  box-shadow: var(--box-shadow-subtle);
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.grid-instructions p {
  display: flex;
  align-items: center;
  margin: 0;
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  font-size: 0.8rem;
  line-height: 1.4;
  letter-spacing: 0.01em;
}

/* Tool Icon Styles */
.tool-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin: 0 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  position: relative;
  flex-shrink: 0;
}

.rectangle-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23e65100" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg>');
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background-color: rgba(230, 81, 0, 0.05);
}

.rectangle-icon:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  background-color: rgba(230, 81, 0, 0.1);
}

/* Highlighted state for the rectangle draw tool */
.leaflet-draw-toolbar .leaflet-draw-draw-rectangle.highlight-tool {
  background-color: var(--action-color);
  box-shadow: 0 0 0 3px rgba(var(--action-color-rgb, 245, 158, 11), 0.3);
}

/* Pulse animation for the active draw tool */
@keyframes draw-tool-pulse {
  0% { box-shadow: 0 0 0 0 rgba(var(--action-color-rgb, 245, 158, 11), 0.7); }
  70% { box-shadow: 0 0 0 6px rgba(var(--action-color-rgb, 245, 158, 11), 0); }
  100% { box-shadow: 0 0 0 0 rgba(var(--action-color-rgb, 245, 158, 11), 0); }
}

/* Adjust Leaflet zoom control position */
.leaflet-control-zoom {
  margin-left: var(--spacing-unit) !important; /* 8px margin */
  margin-top: var(--spacing-unit) !important; /* 8px margin */
}


/* --- Controls Container Styles --- */
#controls-container, #mortar-controls-container {
  display: flex;
  flex-direction: column;
  gap: calc(var(--spacing-unit) * 4); /* 32px gap */
  background-color: var(--surface-color); /* Use surface color */
  background-image: var(--grid-pattern);
  padding: calc(var(--spacing-unit) * 4.5); /* 36px padding */
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--military-olive);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  width: 100%; /* Ensure it takes full width of container */
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

#controls-container::before, #mortar-controls-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--military-olive), transparent);
  opacity: 0.7;
}

/* Enhanced Section & Panel Design */
section {
  padding: calc(var(--spacing-unit) * 3);
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  position: relative;
  margin-bottom: calc(var(--spacing-unit) * 3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08),
              0 1px 3px rgba(0, 0, 0, 0.12);
  transition: all var(--transition-speed) var(--transition-function);
  overflow: hidden;
}

section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12),
              0 2px 6px rgba(0, 0, 0, 0.16);
  transform: translateY(-1px);
}

section:last-child {
  margin-bottom: 0;
}

/* Sophisticated Panel Accent */
section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--military-olive-light), var(--military-olive-dark));
  opacity: 0.8;
}

/* Control Panel Specific Styling */
.control-panel {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: calc(var(--spacing-unit) * 3);
  margin-bottom: calc(var(--spacing-unit) * 3);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1),
              0 1px 4px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-speed) var(--transition-function);
}

.control-panel:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15),
              0 2px 8px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}


.form-group {
  margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
  position: relative;
}

.form-group::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, var(--military-olive-light), transparent);
  opacity: 0.5;
}

label {
  display: block;
  margin-bottom: calc(var(--spacing-unit) * 1.25); /* 10px margin */
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  font-size: 1rem;
  letter-spacing: 0.01em;
  position: relative;
  padding-left: 12px;
}

label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 14px;
  background: var(--military-olive-light);
  opacity: 0.6;
  border-radius: 2px;
}

/* Enhanced Form Elements */
select,
input[type="number"],
input[type="text"] {
  width: 100%;
  padding: calc(var(--spacing-unit) * 1.75) calc(var(--spacing-unit) * 2); /* 14px 16px padding */
  border: 1px solid var(--border-color);
  border-left: 3px solid var(--military-olive);
  border-radius: var(--border-radius);
  box-sizing: border-box;
  font-size: 1rem;
  color: var(--text-dark);
  background: linear-gradient(135deg, var(--background-light), var(--surface-color));
  transition: all var(--transition-speed) var(--transition-function);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08),
              inset 0 1px 2px rgba(0, 0, 0, 0.05);
  font-family: var(--font-family-ui);
  position: relative;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%234b5320' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 35px;
}

/* Enhanced Form States */
select:hover,
input[type="number"]:hover,
input[type="text"]:hover {
  border-color: var(--border-color-hover);
  border-left: 4px solid var(--military-olive-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12),
              inset 0 1px 3px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
  background: linear-gradient(135deg, var(--surface-color), var(--background-light));
}

select:focus,
input[type="number"]:focus,
input[type="text"]:focus {
  outline: none;
  border-color: var(--primary-light);
  border-left: 4px solid var(--primary-color);
  box-shadow: 0 0 0 3px rgba(29, 53, 87, 0.15),
              0 6px 16px rgba(0, 0, 0, 0.15),
              inset 0 1px 3px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 255, 255, 0.9));
  transform: translateY(-1px);
}

/* --- Form Validation Styles --- */
.form-group {
  position: relative;
}

.form-group.has-error input,
.form-group.has-error select {
  border-color: var(--danger-color);
  border-left: 3px solid var(--danger-color);
  background-color: rgba(183, 28, 28, 0.05);
}

.form-group.has-error input:focus,
.form-group.has-error select:focus {
  box-shadow: 0 0 0 3px rgba(183, 28, 28, 0.15);
}

.form-group.has-success input,
.form-group.has-success select {
  border-color: var(--success-color);
  border-left: 3px solid var(--success-color);
  background-color: rgba(46, 125, 50, 0.05);
}

.form-group.has-success input:focus,
.form-group.has-success select:focus {
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.15);
}

.form-group.is-loading input,
.form-group.is-loading select {
  border-color: var(--info-color);
  border-left: 3px solid var(--info-color);
  background-color: rgba(13, 71, 161, 0.05);
}

.validation-message {
  position: absolute;
  bottom: -20px;
  left: 0;
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  opacity: 0;
  transform: translateY(-5px);
  transition: all var(--transition-speed) var(--transition-function);
  pointer-events: none;
  z-index: 10;
}

.validation-message.show {
  opacity: 1;
  transform: translateY(0);
}

.validation-message.error {
  color: var(--danger-color);
}

.validation-message.success {
  color: var(--success-color);
}

.validation-message.info {
  color: var(--info-color);
}

.validation-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  opacity: 0;
  transition: all var(--transition-speed) var(--transition-function);
  pointer-events: none;
  z-index: 5;
}

.form-group.has-error .validation-icon {
  opacity: 1;
  color: var(--danger-color);
}

.form-group.has-success .validation-icon {
  opacity: 1;
  color: var(--success-color);
}

.form-group.is-loading .validation-icon {
  opacity: 1;
  color: var(--info-color);
  animation: spin 1s linear infinite;
}

/* Loading spinner for form elements */
@keyframes form-loading-pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Shake animation for validation errors */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.form-loading {
  position: relative;
}

.form-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(13, 71, 161, 0.1), transparent);
  animation: form-loading-pulse 1.5s infinite;
  pointer-events: none;
  border-radius: var(--border-radius);
}

/* Enhanced Button Styling */
button {
  color: var(--text-light);
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3); /* 12px 24px padding */
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600; /* Slightly bolder for better presence */
  transition: all var(--transition-speed) var(--transition-function);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12),
              0 1px 3px rgba(0, 0, 0, 0.16),
              inset 0 1px 0 rgba(255, 255, 255, 0.15);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(0, 0, 0, 0.05) 100%);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity var(--transition-speed) var(--transition-function);
}

button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(0, 0, 0, 0.2);
  opacity: 1;
  transition: all var(--transition-speed) var(--transition-function);
}

/* Enhanced Button States */
button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.18),
              0 3px 8px rgba(0, 0, 0, 0.22),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(0, 0, 0, 0.02) 100%);
}

button:hover:not(:disabled)::before {
  opacity: 1;
}

button:active:not(:disabled) {
  transform: translateY(0px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15),
              inset 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: none; /* Remove glow on click, will add via JS class */
  background-image: linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.1) 100%);
}

button:active:not(:disabled)::after {
  height: 0;
  opacity: 0;
}

/* Specific Button Colors */
#add-target-button {
  background-color: var(--accent-color); /* Green for add */
}
#add-target-button:hover:not(:disabled) {
   background-color: var(--accent-dark); /* Darker green on hover */
}

/* Target List Button Styles */
.button-group {
  display: flex;
  gap: calc(var(--spacing-unit) * 1.5); /* 12px gap */
  justify-content: flex-end;
  margin-top: calc(var(--spacing-unit) * 1.5); /* 12px top margin */
}

.button-group button {
  padding: calc(var(--spacing-unit) * 0.75) calc(var(--spacing-unit) * 1.5); /* 6px 12px padding */
  font-size: 0.85em;
  border-radius: calc(var(--border-radius) * 0.75); /* Slightly smaller radius */
  min-width: 70px; /* Ensure minimum width */
  text-align: center;
  font-weight: var(--font-weight-semibold);
}

.go-to-button {
  background-color: var(--info-color); /* Blue for info/go to */
}

.go-to-button:hover:not(:disabled) {
  background-color: var(--info-dark); /* Darker blue on hover */
}

.go-to-button:active:not(:disabled) {
  animation: glow-info 0.5s ease-in-out infinite alternate;
}

.remove-button {
  background-color: var(--danger-color); /* Red for remove */
}

.remove-button:hover:not(:disabled) {
  background-color: var(--danger-dark); /* Darker red on hover */
}

.remove-button:active:not(:disabled) {
  animation: glow-danger 0.5s ease-in-out infinite alternate;
}

@keyframes glow-info {
  from { box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(52, 152, 219, 0.5); }
  to { box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(52, 152, 219, 0.8); }
}


#send-targets-button {
  background-color: var(--primary-color); /* Dark blue for send */
}
#send-targets-button:hover:not(:disabled) {
  background-color: var(--primary-dark); /* Darker blue on hover */
}
#send-targets-button:disabled {
  background-color: #a0cfff; /* Lighter blue when disabled */
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Enhanced button states for sending/success/error */
button.sending {
  background-color: var(--info-color) !important;
  position: relative;
  overflow: hidden;
}

button.sending::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: loading-sweep 1.5s infinite;
}

button.success {
  background-color: var(--success-color) !important;
  animation: success-pulse 0.6s ease-out;
}

button.error {
  background-color: var(--danger-color) !important;
  animation: error-shake 0.6s ease-out;
}

.loading-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

/* Button state animations */
@keyframes loading-sweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes success-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes error-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-3px); }
  75% { transform: translateX(3px); }
}

/* Feedback message styles */
.feedback-message {
  padding: calc(var(--spacing-unit) * 1.5);
  margin-top: calc(var(--spacing-unit) * 2);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  text-align: center;
  transition: all var(--transition-speed) var(--transition-function);
  opacity: 0;
  transform: translateY(-10px);
}

.feedback-message.sending {
  background-color: rgba(13, 71, 161, 0.1);
  border: 1px solid var(--info-color);
  color: var(--info-color);
  opacity: 1;
  transform: translateY(0);
}

.feedback-message.success {
  background-color: rgba(46, 125, 50, 0.1);
  border: 1px solid var(--success-color);
  color: var(--success-color);
  opacity: 1;
  transform: translateY(0);
}

.feedback-message.error {
  background-color: rgba(183, 28, 28, 0.1);
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
  opacity: 1;
  transform: translateY(0);
}

/* Temporary message styles */
.temp-message {
  position: absolute;
  bottom: -30px;
  left: 0;
  right: 0;
  padding: calc(var(--spacing-unit) * 1);
  border-radius: var(--border-radius);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  text-align: center;
  opacity: 0;
  transform: translateY(-10px);
  transition: all var(--transition-speed) var(--transition-function);
  z-index: 10;
  pointer-events: none;
}

.temp-message.show {
  opacity: 1;
  transform: translateY(0);
}

.temp-message.warning {
  background-color: rgba(230, 81, 0, 0.1);
  border: 1px solid var(--warning-color);
  color: var(--warning-color);
}

.temp-message.info {
  background-color: rgba(13, 71, 161, 0.1);
  border: 1px solid var(--info-color);
  color: var(--info-color);
}

.temp-message.success {
  background-color: rgba(46, 125, 50, 0.1);
  border: 1px solid var(--success-color);
  color: var(--success-color);
}

/* Calculating button state */
button.calculating {
  background-color: var(--info-color) !important;
  position: relative;
  overflow: hidden;
}

button.calculating::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: loading-sweep 1.5s infinite;
}

/* --- Tooltip System --- */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.tooltip {
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--text-dark);
  color: var(--text-light);
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2);
  border-radius: var(--border-radius);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
  white-space: nowrap;
  max-width: 300px;
  white-space: normal;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed) var(--transition-function);
  z-index: 1000;
  box-shadow: var(--box-shadow-large);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: var(--text-dark);
}

.tooltip-container:hover .tooltip,
.tooltip-container:focus .tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-5px);
}

/* Tooltip variants */
.tooltip.tooltip-military {
  background-color: var(--military-olive-dark);
  border-color: var(--military-olive);
}

.tooltip.tooltip-military::after {
  border-top-color: var(--military-olive-dark);
}

.tooltip.tooltip-technical {
  background-color: var(--info-color);
  border-color: var(--info-light);
}

.tooltip.tooltip-technical::after {
  border-top-color: var(--info-color);
}

.tooltip.tooltip-warning {
  background-color: var(--warning-color);
  border-color: var(--action-light);
}

.tooltip.tooltip-warning::after {
  border-top-color: var(--warning-color);
}

/* Tooltip positioning variants */
.tooltip.tooltip-top {
  bottom: 125%;
  top: auto;
}

.tooltip.tooltip-bottom {
  top: 125%;
  bottom: auto;
}

.tooltip.tooltip-bottom::after {
  top: -12px;
  border-top-color: transparent;
  border-bottom-color: var(--text-dark);
}

.tooltip.tooltip-left {
  left: auto;
  right: 125%;
  top: 50%;
  transform: translateY(-50%);
}

.tooltip.tooltip-left::after {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: var(--text-dark);
  border-top-color: transparent;
}

.tooltip.tooltip-right {
  left: 125%;
  right: auto;
  top: 50%;
  transform: translateY(-50%);
}

.tooltip.tooltip-right::after {
  right: 100%;
  left: auto;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: var(--text-dark);
  border-top-color: transparent;
}

/* Help icon styles */
.help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--info-color);
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: calc(var(--spacing-unit) * 0.5);
  cursor: help;
  transition: all var(--transition-speed) var(--transition-function);
}

.help-icon:hover {
  background-color: var(--info-dark);
  transform: scale(1.1);
}

/* Military term highlighting */
.military-term {
  border-bottom: 1px dotted var(--military-olive);
  cursor: help;
  position: relative;
}

.technical-term {
  border-bottom: 1px dotted var(--info-color);
  cursor: help;
  position: relative;
}

/* --- Confirmation Dialog System --- */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(3px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed) var(--transition-function);
}

.confirmation-overlay.show {
  opacity: 1;
  visibility: visible;
}

.confirmation-dialog {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  border: 2px solid var(--border-color);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  transform: scale(0.8) translateY(-20px);
  transition: all var(--transition-speed) var(--transition-function);
}

.confirmation-overlay.show .confirmation-dialog {
  transform: scale(1) translateY(0);
}

.confirmation-header {
  padding: calc(var(--spacing-unit) * 3);
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--warning-color), var(--action-color));
  color: white;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.confirmation-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  gap: calc(var(--spacing-unit) * 1);
}

.confirmation-header .warning-icon {
  font-size: 1.4rem;
  animation: pulse-warning 2s infinite;
}

.confirmation-body {
  padding: calc(var(--spacing-unit) * 3);
}

.confirmation-message {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: calc(var(--spacing-unit) * 2);
  color: var(--text-dark);
}

.confirmation-preview {
  background-color: var(--background-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: calc(var(--spacing-unit) * 2);
  margin: calc(var(--spacing-unit) * 2) 0;
  max-height: 200px;
  overflow-y: auto;
}

.confirmation-preview h4 {
  margin: 0 0 calc(var(--spacing-unit) * 1) 0;
  font-size: 0.9rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.confirmation-preview-item {
  padding: calc(var(--spacing-unit) * 0.75);
  margin: calc(var(--spacing-unit) * 0.5) 0;
  background-color: rgba(183, 28, 28, 0.1);
  border-left: 3px solid var(--danger-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
  font-family: var(--font-family-mono);
}

.confirmation-actions {
  display: flex;
  gap: calc(var(--spacing-unit) * 2);
  justify-content: flex-end;
  padding: calc(var(--spacing-unit) * 3);
  border-top: 1px solid var(--border-color);
  background-color: var(--background-light);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

.confirmation-button {
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3);
  border: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  min-width: 100px;
}

.confirmation-button.cancel {
  background-color: var(--surface-color);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
}

.confirmation-button.cancel:hover {
  background-color: var(--background-light);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-subtle);
}

.confirmation-button.confirm {
  background-color: var(--danger-color);
  color: white;
  position: relative;
  overflow: hidden;
}

.confirmation-button.confirm:hover {
  background-color: var(--danger-dark);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-medium);
}

.confirmation-button.confirm:active {
  transform: translateY(0);
}

/* Confirmation button loading state */
.confirmation-button.confirm.loading {
  pointer-events: none;
}

.confirmation-button.confirm.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: loading-sweep 1.5s infinite;
}

/* Responsive confirmation dialog */
@media (max-width: 768px) {
  .confirmation-dialog {
    width: 95%;
    margin: calc(var(--spacing-unit) * 2);
  }

  .confirmation-actions {
    flex-direction: column;
  }

  .confirmation-button {
    width: 100%;
  }
}

/* --- Map Tools Styles --- */
#measure-distance-button {
  margin-top: calc(var(--spacing-unit) * 1);
  background-color: var(--info-color);
  color: white;
  border: none;
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  display: flex;
  align-items: center;
  gap: calc(var(--spacing-unit) * 0.75);
  width: 100%;
  justify-content: center;
}

#measure-distance-button:hover {
  background-color: var(--info-dark);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-medium);
}

#measure-distance-button.active {
  background-color: var(--warning-color);
  animation: pulse-warning 2s infinite;
}

#measure-distance-button:active {
  transform: translateY(0);
}

/* Map cursor styles */
.leaflet-container.measurement-mode {
  cursor: crosshair !important;
}

/* Measurement popup styles */
.leaflet-popup-content {
  font-family: var(--font-family-ui);
  line-height: 1.4;
}

.leaflet-popup-content button {
  background-color: var(--danger-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.leaflet-popup-content button:hover {
  background-color: var(--danger-dark);
}

/* --- Zoom Controls Styles --- */
.zoom-controls {
  margin-top: calc(var(--spacing-unit) * 2);
  padding: calc(var(--spacing-unit) * 2);
  background-color: var(--background-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.zoom-controls label {
  display: block;
  margin-bottom: calc(var(--spacing-unit) * 1);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.zoom-buttons {
  display: flex;
  gap: calc(var(--spacing-unit) * 0.75);
  margin-bottom: calc(var(--spacing-unit) * 1.5);
  flex-wrap: wrap;
}

.zoom-buttons button {
  flex: 1;
  min-width: 80px;
  padding: calc(var(--spacing-unit) * 1) calc(var(--spacing-unit) * 1.5);
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--spacing-unit) * 0.5);
}

.zoom-buttons button:hover {
  background-color: var(--primary-light);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-subtle);
}

.zoom-buttons button:active {
  transform: translateY(0);
}

.zoom-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(var(--spacing-unit) * 1);
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.zoom-indicator span:last-child {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  margin-left: calc(var(--spacing-unit) * 0.5);
}

/* Responsive zoom controls */
@media (max-width: 768px) {
  .zoom-buttons {
    flex-direction: column;
  }

  .zoom-buttons button {
    width: 100%;
  }
}

/* --- Target Summary Dashboard --- */
#target-summary-section {
  margin-bottom: calc(var(--spacing-unit) * 3);
}

.summary-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: calc(var(--spacing-unit) * 1.5);
  margin-bottom: calc(var(--spacing-unit) * 2);
}

/* Enhanced Target Summary Cards */
.summary-card {
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 255, 255, 0.9));
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: calc(var(--spacing-unit) * 2.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed) var(--transition-function);
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08),
              0 1px 4px rgba(0, 0, 0, 0.12),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--military-olive-light), var(--military-olive-dark), var(--military-olive-light));
  transition: all var(--transition-speed) var(--transition-function);
  opacity: 0.8;
}

.summary-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15),
              0 3px 8px rgba(0, 0, 0, 0.18),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: var(--primary-light);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), var(--surface-color));
}

.summary-card:hover::before {
  height: 6px;
  opacity: 1;
}

.summary-card:hover::after {
  left: 100%;
}

/* Enhanced Summary Icons */
.summary-icon {
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(29, 53, 87, 0.15), rgba(29, 53, 87, 0.05));
  transition: all var(--transition-speed) var(--transition-function);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.summary-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity var(--transition-speed) var(--transition-function);
}

.summary-card:hover .summary-icon::before {
  opacity: 1;
}

.summary-icon.enemy {
  background: linear-gradient(135deg, rgba(183, 28, 28, 0.25), rgba(183, 28, 28, 0.12));
  color: var(--danger-color);
  box-shadow: 0 3px 8px rgba(183, 28, 28, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(183, 28, 28, 0.3);
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.summary-icon.friendly {
  background: linear-gradient(135deg, rgba(13, 71, 161, 0.25), rgba(13, 71, 161, 0.12));
  color: var(--info-color);
  box-shadow: 0 3px 8px rgba(13, 71, 161, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(13, 71, 161, 0.3);
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.summary-icon.priority {
  background: linear-gradient(135deg, rgba(230, 81, 0, 0.25), rgba(230, 81, 0, 0.12));
  color: var(--warning-color);
  animation: pulse-priority 3s infinite;
  box-shadow: 0 3px 8px rgba(230, 81, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(230, 81, 0, 0.3);
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.summary-content {
  flex: 1;
  text-align: center;
}

/* Enhanced Summary Content */
.summary-number {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-dark);
  line-height: 1;
  margin-bottom: calc(var(--spacing-unit) * 0.75);
  transition: all var(--transition-speed) var(--transition-function);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--text-medium);
  text-transform: uppercase;
  letter-spacing: 0.08em;
  font-weight: 600;
  opacity: 0.8;
  transition: all var(--transition-speed) var(--transition-function);
}

.summary-card:hover .summary-number {
  color: var(--primary-color);
  transform: scale(1.05);
}

.summary-card:hover .summary-label {
  opacity: 1;
  color: var(--text-dark);
}

/* Summary card animations */
@keyframes pulse-priority {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.summary-card.updated .summary-number {
  animation: number-update 0.6s ease-out;
}

@keyframes number-update {
  0% {
    transform: scale(1.2);
    color: var(--primary-color);
  }
  100% {
    transform: scale(1);
    color: var(--text-dark);
  }
}

/* Responsive summary dashboard */
@media (max-width: 768px) {
  .summary-dashboard {
    grid-template-columns: repeat(2, 1fr);
  }

  .summary-card {
    padding: calc(var(--spacing-unit) * 1.5);
  }

  .summary-icon {
    font-size: 1.5rem;
    width: 35px;
    height: 35px;
  }

  .summary-number {
    font-size: 1.5rem;
  }
}

/* --- Range Rings Styles --- */
#toggle-range-rings {
  background-color: var(--warning-color);
  color: white;
  border: none;
  padding: calc(var(--spacing-unit) * 2) calc(var(--spacing-unit) * 3);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--spacing-unit) * 0.75);
  flex: 1;
  min-width: 180px;
  font-weight: 600;
  font-size: 1rem;
}

#toggle-range-rings:hover {
  background-color: var(--action-color);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-medium);
}

#toggle-range-rings.active {
  background-color: var(--success-color);
  animation: pulse-success 2s infinite;
}

/* Responsive layout for calculation buttons */
@media (max-width: 768px) {
  .calculation-buttons {
    flex-direction: column;
  }

  #calculate-solution-button,
  #toggle-range-rings {
    min-width: unset;
    width: 100%;
  }
}

#toggle-range-rings:active {
  transform: translateY(0);
}

.range-tooltip {
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  border: none !important;
  border-radius: var(--border-radius-sm) !important;
  font-size: 0.8rem !important;
  font-weight: var(--font-weight-medium) !important;
  padding: calc(var(--spacing-unit) * 0.75) calc(var(--spacing-unit) * 1) !important;
}

/* --- Enhanced Target List Display --- */
#target-list-items li,
#received-targets-list li {
  background: linear-gradient(135deg, var(--surface-color), var(--background-light));
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--primary-color);
  border-radius: var(--border-radius);
  padding: calc(var(--spacing-unit) * 2);
  margin-bottom: calc(var(--spacing-unit) * 1.5);
  transition: all var(--transition-speed) var(--transition-function);
  position: relative;
  overflow: hidden;
}

#target-list-items li:hover,
#received-targets-list li:hover {
  transform: translateX(4px);
  box-shadow: var(--box-shadow-medium);
  border-left-color: var(--secondary-color);
}

#target-list-items li.selected,
#received-targets-list li.selected {
  border-left-color: var(--success-color);
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.1), var(--surface-color));
  box-shadow: var(--box-shadow-medium);
}

.target-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--spacing-unit) * 1);
}

.target-type-badge {
  display: inline-flex;
  align-items: center;
  gap: calc(var(--spacing-unit) * 0.5);
  padding: calc(var(--spacing-unit) * 0.5) calc(var(--spacing-unit) * 1);
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.target-type-badge.enemy {
  background-color: var(--danger-color);
}

.target-type-badge.friendly {
  background-color: var(--info-color);
}

.target-priority-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--warning-color);
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: calc(var(--spacing-unit) * 0.5);
}

.target-priority-indicator.high {
  background-color: var(--danger-color);
  animation: pulse-danger 2s infinite;
}

.target-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: calc(var(--spacing-unit) * 1);
  font-size: 0.9rem;
  color: var(--text-muted);
}

.target-detail-item {
  display: flex;
  align-items: center;
  gap: calc(var(--spacing-unit) * 0.5);
}

.target-detail-icon {
  font-size: 0.8rem;
  opacity: 0.7;
}

/* Responsive target list */
@media (max-width: 768px) {
  .target-details {
    grid-template-columns: 1fr;
  }

  .target-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: calc(var(--spacing-unit) * 0.75);
  }
}

/* --- Keyboard Shortcuts Help Styles --- */
.shortcuts-help {
  max-width: 600px;
  font-family: var(--font-family-ui);
}

.shortcuts-help h3 {
  margin: 0 0 calc(var(--spacing-unit) * 2) 0;
  color: var(--primary-color);
  text-align: center;
  font-size: 1.3rem;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: calc(var(--spacing-unit) * 2);
}

.shortcut-category {
  background-color: var(--background-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: calc(var(--spacing-unit) * 2);
}

.shortcut-category h4 {
  margin: 0 0 calc(var(--spacing-unit) * 1.5) 0;
  color: var(--text-dark);
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: calc(var(--spacing-unit) * 0.75);
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: calc(var(--spacing-unit) * 0.75) 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
}

.shortcut-item:last-child {
  border-bottom: none;
}

kbd {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: calc(var(--spacing-unit) * 0.25) calc(var(--spacing-unit) * 0.75);
  font-family: var(--font-family-mono);
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

/* Quick Action Toolbar Styles */
.quick-action-toolbar {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  padding: calc(var(--spacing-unit) * 1);
  display: flex;
  flex-direction: column;
  gap: calc(var(--spacing-unit) * 0.75);
  z-index: 1000;
  opacity: 0.9;
  transition: all var(--transition-speed) var(--transition-function);
}

.quick-action-toolbar:hover {
  opacity: 1;
  transform: translateY(-50%) translateX(-5px);
}

.quick-action-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--border-radius);
  background-color: var(--primary-color);
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.quick-action-button:hover {
  background-color: var(--primary-dark);
  transform: scale(1.1);
}

.quick-action-button.active {
  background-color: var(--success-color);
}

.quick-action-button::after {
  content: attr(data-tooltip);
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--text-dark);
  color: white;
  padding: calc(var(--spacing-unit) * 0.5) calc(var(--spacing-unit) * 1);
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed) var(--transition-function);
  margin-right: calc(var(--spacing-unit) * 1);
  z-index: 10;
}

.quick-action-button:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Responsive quick action toolbar */
@media (max-width: 768px) {
  .quick-action-toolbar {
    position: fixed;
    bottom: 20px;
    right: 20px;
    top: auto;
    transform: none;
    flex-direction: row;
    border-radius: var(--border-radius-lg);
  }

  .quick-action-toolbar:hover {
    transform: translateY(-5px);
  }

  .quick-action-button::after {
    bottom: 100%;
    right: 50%;
    top: auto;
    transform: translateX(50%);
    margin-right: 0;
    margin-bottom: calc(var(--spacing-unit) * 1);
  }
}

/* --- Batch Operations Styles --- */
.batch-operations-panel {
  position: fixed;
  top: 20px;
  left: 20px;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  min-width: 300px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-20px);
  transition: all var(--transition-speed) var(--transition-function);
}

.batch-operations-panel.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.batch-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: calc(var(--spacing-unit) * 2);
  background: linear-gradient(135deg, var(--info-color), var(--primary-color));
  color: white;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.batch-panel-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: var(--font-weight-semibold);
}

.batch-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: calc(var(--spacing-unit) * 0.5);
  border-radius: var(--border-radius-sm);
  transition: background-color var(--transition-speed);
}

.batch-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.batch-panel-content {
  padding: calc(var(--spacing-unit) * 2);
}

.batch-stats {
  margin-bottom: calc(var(--spacing-unit) * 2);
  font-size: 0.9rem;
  color: var(--text-muted);
  text-align: center;
  padding: calc(var(--spacing-unit) * 1);
  background-color: var(--background-light);
  border-radius: var(--border-radius-sm);
}

.batch-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: calc(var(--spacing-unit) * 1);
}

.batch-action-btn {
  padding: calc(var(--spacing-unit) * 1.5);
  border: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  font-size: 0.9rem;
}

.batch-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-subtle);
}

.batch-action-btn:not(.primary):not(.danger) {
  background-color: var(--surface-color);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
}

.batch-action-btn.primary {
  background-color: var(--primary-color);
  color: white;
}

.batch-action-btn.primary:hover {
  background-color: var(--primary-dark);
}

.batch-action-btn.danger {
  background-color: var(--danger-color);
  color: white;
}

.batch-action-btn.danger:hover {
  background-color: var(--danger-dark);
}

/* Batch mode target list styles */
.target-list-items li.batch-mode {
  position: relative;
  padding-left: calc(var(--spacing-unit) * 4);
}

.batch-checkbox {
  position: absolute;
  left: calc(var(--spacing-unit) * 1);
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.target-list-items li.batch-selected {
  background: linear-gradient(135deg, rgba(13, 71, 161, 0.1), var(--surface-color));
  border-left-color: var(--info-color);
  box-shadow: var(--box-shadow-medium);
}

/* Responsive batch operations */
@media (max-width: 768px) {
  .batch-operations-panel {
    left: 10px;
    right: 10px;
    min-width: auto;
  }

  .batch-actions {
    grid-template-columns: 1fr;
  }
}

/* --- Accessibility & Field Use Enhancements --- */

/* High Contrast Mode */
.high-contrast-mode {
  --primary-color: #000000;
  --primary-light: #333333;
  --primary-dark: #000000;
  --secondary-color: #ffffff;
  --accent-color: #ffff00;
  --background-color: #ffffff;
  --background-light: #f0f0f0;
  --surface-color: #ffffff;
  --text-dark: #000000;
  --text-light: #ffffff;
  --text-muted: #333333;
  --border-color: #000000;
  --success-color: #008000;
  --danger-color: #ff0000;
  --warning-color: #ff8000;
  --info-color: #0000ff;
  --military-olive: #000000;
  --military-olive-dark: #000000;
}

.high-contrast-mode button,
.high-contrast-mode .control-panel,
.high-contrast-mode .status-bar,
.high-contrast-mode .summary-card {
  border: 2px solid #000000 !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

.high-contrast-mode button:hover {
  background-color: #ffff00 !important;
  color: #000000 !important;
}

.high-contrast-mode .nav-button.active {
  background-color: #000000 !important;
  color: #ffffff !important;
}

/* Night Vision Mode */
.night-vision-mode {
  --primary-color: #ff0000;
  --primary-light: #ff3333;
  --primary-dark: #cc0000;
  --secondary-color: #ff0000;
  --accent-color: #ff0000;
  --background-color: #000000;
  --background-light: #111111;
  --surface-color: #000000;
  --text-dark: #ff0000;
  --text-light: #ff0000;
  --text-muted: #cc0000;
  --border-color: #ff0000;
  --success-color: #ff0000;
  --danger-color: #ff0000;
  --warning-color: #ff0000;
  --info-color: #ff0000;
  --military-olive: #ff0000;
  --military-olive-dark: #cc0000;
}

.night-vision-mode * {
  color: #ff0000 !important;
  border-color: #ff0000 !important;
}

.night-vision-mode button,
.night-vision-mode .control-panel,
.night-vision-mode .status-bar,
.night-vision-mode input,
.night-vision-mode select {
  background-color: #000000 !important;
  color: #ff0000 !important;
  border: 1px solid #ff0000 !important;
}

.night-vision-mode .leaflet-container {
  filter: hue-rotate(180deg) invert(1) contrast(2);
}

/* Large Touch Targets Mode */
.large-touch-mode button,
.large-touch-mode .quick-action-button,
.large-touch-mode input,
.large-touch-mode select {
  min-height: 48px !important;
  min-width: 48px !important;
  padding: calc(var(--spacing-unit) * 2) !important;
  font-size: 1.1rem !important;
}

.large-touch-mode .target-list-items li {
  padding: calc(var(--spacing-unit) * 3) !important;
  margin-bottom: calc(var(--spacing-unit) * 2) !important;
}

.large-touch-mode .status-indicator {
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2) !important;
  font-size: 1rem !important;
}

.large-touch-mode .summary-card {
  padding: calc(var(--spacing-unit) * 3) !important;
}

.large-touch-mode .zoom-buttons button {
  min-height: 48px !important;
  font-size: 1rem !important;
}

/* Accessibility Controls Panel */
.accessibility-controls {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  padding: calc(var(--spacing-unit) * 2);
  z-index: 1002;
  display: none;
  min-width: 300px;
}

.accessibility-controls.show {
  display: block;
}

.accessibility-controls h4 {
  margin: 0 0 calc(var(--spacing-unit) * 2) 0;
  text-align: center;
  color: var(--primary-color);
}

.accessibility-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--spacing-unit) * 1.5);
  padding: calc(var(--spacing-unit) * 1);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.accessibility-option label {
  font-weight: var(--font-weight-medium);
  cursor: pointer;
}

.accessibility-toggle {
  position: relative;
  width: 50px;
  height: 24px;
  background-color: var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.accessibility-toggle.active {
  background-color: var(--success-color);
}

.accessibility-toggle::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: transform var(--transition-speed);
}

.accessibility-toggle.active::after {
  transform: translateX(26px);
}

/* Focus indicators for keyboard navigation */
button:focus,
input:focus,
select:focus,
.quick-action-button:focus {
  outline: 3px solid var(--accent-color) !important;
  outline-offset: 2px !important;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}


#refresh-targets-button {
  background-color: var(--primary-color); /* Dark blue for refresh */
}
#refresh-targets-button:hover:not(:disabled) {
   background-color: var(--primary-dark); /* Darker blue on hover */
}
#refresh-targets-button:disabled {
   background-color: #a0cfff; /* Lighter blue when disabled */
   cursor: not-allowed;
   box-shadow: none;
   transform: none;
}

/* Calculation Controls Layout */
.calculation-controls {
  display: flex;
  flex-direction: column;
  gap: calc(var(--spacing-unit) * 2);
  margin-top: calc(var(--spacing-unit) * 2);
}

.calculation-buttons {
  display: flex;
  gap: calc(var(--spacing-unit) * 2);
  flex-wrap: wrap;
}

#calculate-solution-button {
  background-color: var(--accent-color); /* Emerald green for calculate */
  flex: 1;
  min-width: 180px;
  font-weight: 600;
  font-size: 1rem;
  padding: calc(var(--spacing-unit) * 2) calc(var(--spacing-unit) * 3);
}
#calculate-solution-button:hover:not(:disabled) {
   background-color: var(--accent-dark); /* Darker green on hover */
}
#calculate-solution-button:disabled {
  background-color: #d4edda; /* Lighter green when disabled */
  color: #155724;
  cursor: not-allowed;
  border-color: #c3e6cb;
  box-shadow: none;
  transform: none;
}


#clear-targets-button,
#clear-targets-button-mortar,
#clear-grid-lines-button {
  background-color: var(--danger-color); /* Red for clear */
  margin-top: calc(var(--spacing-unit) * 3); /* 24px top margin */
  width: 100%; /* Make button full width */
  margin-bottom: calc(var(--spacing-unit) * 1.5); /* Add bottom margin */
  font-size: 0.95rem; /* Slightly smaller font */
}
#clear-targets-button:hover:not(:disabled),
#clear-targets-button-mortar:hover:not(:disabled),
#clear-grid-lines-button:hover:not(:disabled) {
  background-color: var(--danger-dark); /* Darker red on hover */
}
#clear-grid-lines-button:active:not(:disabled) {
  animation: glow-danger 0.5s ease-in-out infinite alternate;
}

#draw-grid-button { /* Styling for the new Draw Grid Area button */
   background-color: var(--action-color); /* Orange for action */
}
#draw-grid-button:hover:not(:disabled) {
   background-color: var(--action-dark); /* Darker orange on hover */
}


/* Glow Animation (Applied via JS class on Active) */
.button-glow {
   animation: glow 0.5s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
      box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(var(--accent-color, 44, 62, 80), 0.5);
  }
  to {
      box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(var(--accent-color, 44, 62, 80), 0.8);
  }
}

/* Specific Glows (optional, for different colors) */
.glow-green {
  animation-name: glow-green;
}
@keyframes glow-green {
  from { box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(39, 174, 96, 0.5); } /* Using RGB values for variables in rgba */
  to { box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(39, 174, 96, 0.8); }
}

.glow-primary {
  animation-name: glow-primary;
}
@keyframes glow-primary {
   from { box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(44, 62, 80, 0.5); }
  to { box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(44, 62, 80, 0.8); }
}

.glow-danger {
  animation-name: glow-danger;
}
@keyframes glow-danger {
   from { box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(231, 76, 60, 0.5); }
  to { box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(231, 76, 60, 0.8); }
}

.glow-info { /* Specific glow for info color */
  animation-name: glow-info;
}

/* --- Table Styles --- */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
  font-size: 0.95rem;
  box-shadow: var(--box-shadow-medium);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
  background-color: var(--surface-color);
  position: relative;
}

table::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--military-olive);
  z-index: 1;
}

th {
  background-color: var(--primary-color);
  color: var(--text-light);
  font-weight: var(--font-weight-semibold);
  text-align: left;
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2); /* 12px 16px padding */
  border-bottom: 2px solid var(--primary-dark);
  position: relative;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(0, 0, 0, 0.05));
}

th:first-child {
  padding-left: calc(var(--spacing-unit) * 3);
}

td {
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2); /* 12px 16px padding */
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color);
  transition: background-color var(--transition-speed) var(--transition-function);
}

td:first-child {
  padding-left: calc(var(--spacing-unit) * 3);
  position: relative;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) td {
  background-color: var(--background-light);
}

tr:hover td {
  background-color: var(--background-medium);
}

/* --- Mortar-specific Styles --- */

/* Mortar Position Display */
#current-mortar-pos {
  padding: calc(var(--spacing-unit) * 1.5); /* 12px padding */
  background-color: var(--background-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: var(--font-family-data); /* Use data font for coordinates */
  font-size: 1em;
  text-align: center;
  color: var(--text-dark);
}

/* Weapon Selection Layout */
.weapon-selection {
  display: flex;
  flex-wrap: wrap;
  gap: calc(var(--spacing-unit) * 3); /* 24px gap */
  align-items: flex-start;
}

/* Calculation Solution Display */
#calculated-solution-display {
  margin-top: calc(var(--spacing-unit) * 3); /* 24px margin */
  padding: calc(var(--spacing-unit) * 3); /* 24px padding */
  background-color: var(--background-light);
  background-image: var(--grid-pattern);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--military-olive);
  border-radius: var(--border-radius);
  text-align: left;
  min-height: 120px;
  font-size: 1em;
  line-height: 1.6;
  color: var(--text-dark);
  box-shadow: var(--box-shadow-medium);
  position: relative;
  transition: all var(--transition-speed) ease;
  font-family: var(--font-family-data); /* Use data font for solution display */
  overflow: hidden;
}

#calculated-solution-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
  pointer-events: none;
  opacity: 0.5;
}

#calculated-solution-display h3 {
  margin-top: 0;
  margin-bottom: calc(var(--spacing-unit) * 2); /* 16px margin */
  text-align: center;
  color: var(--primary-color);
  font-size: 1.3em;
  font-weight: 600;
}

#calculated-solution-display p {
  margin: var(--spacing-unit) 0; /* 8px margin */
}

#calculated-solution-display hr {
  margin: calc(var(--spacing-unit) * 2.5) 0; /* 20px margin */
  border: 0;
  border-top: 1px solid #ccc;
}

#calculated-solution-display.calculated {
  background-color: #d4edda; /* Light green */
  border-color: #c3e6cb; /* Lighter green */
}

#calculated-solution-display .calculation-confirmation {
  position: absolute;
  top: var(--spacing-unit); /* 8px from top */
  right: var(--spacing-unit); /* 8px from right */
  font-size: 0.8em;
  color: green;
  font-weight: bold;
}

/* Target Display Area */
#target-display-area {
  width: 100%; /* Ensure it takes full width of container */
  box-sizing: border-box;
}

.target-list-controls {
  text-align: center;
  margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
}

#received-targets-list, #target-list-items {
  list-style-type: none;
  padding: 0;
  margin: 0;
  max-height: 350px;
  overflow-y: auto;
}

/* Target List Item Styling */
#target-list-items li {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-left: 3px solid var(--military-olive);
  border-radius: var(--border-radius);
  padding: calc(var(--spacing-unit) * 2); /* 16px padding */
  margin-bottom: calc(var(--spacing-unit) * 1.5); /* 12px margin between items */
  box-shadow: var(--box-shadow-subtle);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

#target-list-items li::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

#target-list-items li:hover {
  box-shadow: var(--box-shadow-medium);
  border-color: var(--border-color-hover);
  border-left-color: var(--military-olive-light);
  transform: translateY(-2px);
}

#target-list-items li:last-child {
  margin-bottom: 0;
}

#target-list-items li.new-target {
  animation: highlight-new 1s ease-out;
}

@keyframes highlight-new {
  0% { background-color: rgba(39, 174, 96, 0.2); }
  100% { background-color: var(--surface-color); }
}

/* Marker Styles */
.target-marker {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease-in-out; /* Smooth transition for marker effects */
  z-index: 900; /* Ensure markers appear above map elements but below mortar */
}

.target-marker.selected {
  transform: scale(1.2); /* Slightly enlarge selected marker */
  z-index: 950; /* Selected markers appear above other markers */
}

/* Enemy marker glow animation */
@keyframes enemy-marker-glow {
  0% { box-shadow: 0 0 0 0 rgba(200, 0, 0, 0.7); }
  50% { box-shadow: 0 0 4px 2px rgba(200, 0, 0, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(200, 0, 0, 0.7); }
}

/* Friendly marker glow animation */
@keyframes friendly-marker-glow {
  0% { box-shadow: 0 0 0 0 rgba(0, 0, 200, 0.7); }
  50% { box-shadow: 0 0 4px 2px rgba(0, 0, 200, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(0, 0, 200, 0.7); }
}

.target-marker.target-enemy div {
  animation: enemy-marker-glow 2s infinite;
  box-sizing: border-box;
  position: relative;
}

.target-marker.target-friendly div {
  animation: friendly-marker-glow 2s infinite;
  box-sizing: border-box;
  position: relative;
}

/* Add inner details for better visibility */
.target-marker.target-enemy div::after,
.target-marker.target-friendly div::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3px;
  height: 3px;
  background-color: white;
  border-radius: 50%;
  opacity: 0.8;
}

/* Mortar Position Marker Styles */
.mortar-position-marker {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease-in-out;
}

/* Mortar glow animation */
@keyframes mortar-glow {
  0% { box-shadow: 0 0 0 0 rgba(0, 100, 0, 0.7); }
  50% { box-shadow: 0 0 5px 2px rgba(0, 100, 0, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(0, 100, 0, 0.7); }
}

.mortar-position-marker div {
  animation: mortar-glow 2s infinite;
  z-index: 1000; /* Ensure it appears above other map elements */
  box-sizing: border-box;
  position: relative;
}

/* Add a small inner dot for better visibility */
.mortar-position-marker div::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: white;
  border-radius: 50%;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  font-size: 1.2em;
  color: var(--primary-color);
}

.loading-overlay::after {
  content: '';
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* --- Theme Settings --- */
#theme-settings {
  margin-top: 20px;
}

.theme-toggle-button {
  width: 100%;
  padding: 10px;
  margin-top: 10px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s;
  text-align: center;
}

.theme-toggle-button:hover {
  background-color: var(--primary-dark);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

/* Floating Theme Toggle Button */
#theme-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--military-olive);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: all 0.3s ease;
  overflow: hidden;
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  letter-spacing: 0.5px;
}

#theme-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(0, 0, 0, 0.1));
  pointer-events: none;
}

#theme-toggle::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

#theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

#theme-toggle:hover::after {
  opacity: 1;
}

/* --- Screen Navigation Styles --- */
.screen-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--spacing-unit) * 2);
  padding: calc(var(--spacing-unit) * 3);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  color: white;
}

/* --- Enhanced Status Bar Styles --- */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--spacing-unit) * 4);
  padding: calc(var(--spacing-unit) * 2.5);
  background-color: var(--surface-color);
  background-image: var(--grid-pattern);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--military-olive);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1),
              0 2px 6px rgba(0, 0, 0, 0.15),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  pointer-events: none;
  opacity: 0.6;
}

.status-bar::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, var(--military-olive-light), var(--military-olive-dark), var(--military-olive-light));
  opacity: 0.7;
}

.status-section {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* Enhanced Status Indicators */
.status-indicator {
  display: flex;
  align-items: center;
  gap: calc(var(--spacing-unit) * 0.75);
  padding: calc(var(--spacing-unit) * 1) calc(var(--spacing-unit) * 1.5);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all var(--transition-speed) var(--transition-function);
  font-size: 0.875rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.status-indicator:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
              inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.status-indicator:hover::before {
  left: 100%;
}

/* Enhanced Status Icons */
.status-icon {
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: all var(--transition-speed) var(--transition-function);
  font-weight: 600;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.status-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity var(--transition-speed) var(--transition-function);
}

.status-indicator:hover .status-icon::before {
  opacity: 1;
}

.status-text {
  color: var(--text-dark);
  font-family: var(--font-family-ui);
  letter-spacing: 0.01em;
}

/* Connection Status Colors */
#connection-status.connected .status-icon {
  color: var(--success-color);
  animation: pulse-success 2s infinite;
}

#connection-status.disconnected .status-icon {
  color: var(--danger-color);
  animation: pulse-danger 1s infinite;
}

#connection-status.connecting .status-icon {
  color: var(--warning-color);
  animation: pulse-warning 1.5s infinite;
}

/* Auto-save Status Colors */
#auto-save-status.saving .status-icon {
  color: var(--info-color);
  animation: spin 1s linear infinite;
}

#auto-save-status.saved .status-icon {
  color: var(--success-color);
}

#auto-save-status.error .status-icon {
  color: var(--danger-color);
}

/* Mortar Ready Status Colors */
#mortar-ready-status.ready .status-icon {
  color: var(--success-color);
}

#mortar-ready-status.not-ready .status-icon {
  color: var(--warning-color);
}

#mortar-ready-status.error .status-icon {
  color: var(--danger-color);
}

/* Status Animations */
@keyframes pulse-success {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes pulse-danger {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.4; }
}

@keyframes pulse-warning {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.screen-navigation h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-buttons {
  display: flex;
  gap: calc(var(--spacing-unit) * 2);
}

.nav-button {
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-md);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.nav-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-button.active {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border-color: white;
  font-weight: 700;
}

.nav-button:disabled {
  cursor: not-allowed;
  opacity: 1;
}

/* Responsive navigation */
@media (max-width: 768px) {
  .screen-navigation {
    flex-direction: column;
    gap: calc(var(--spacing-unit) * 2);
    text-align: center;
  }

  .screen-navigation h1 {
    font-size: 1.5rem;
  }

  .nav-buttons {
    width: 100%;
    justify-content: center;
  }

  .nav-button {
    flex: 1;
    max-width: 150px;
  }

  /* Responsive status bar */
  .status-bar {
    flex-wrap: wrap;
    gap: calc(var(--spacing-unit) * 1);
    justify-content: center;
  }

  .status-section {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .status-indicator {
    font-size: 0.8rem;
    padding: calc(var(--spacing-unit) * 0.5) calc(var(--spacing-unit) * 1);
  }
}

/* --- Responsive Design --- */
@media (min-width: 992px) {
  /* Desktop layout - Map and controls side by side */
  .map-controls-wrapper {
    flex-direction: row;
    align-items: flex-start;
    justify-content: center; /* Center the content horizontally */
    gap: calc(var(--spacing-unit) * 3); /* 24px gap between map and controls */
  }

  #map-container, #mortar-map-container {
    width: 55%; /* Slightly reduced from 60% */
    margin-bottom: 0;
    position: sticky;
    top: calc(var(--spacing-unit) * 4); /* Stick to the top with some padding */
    max-height: calc(100vh - calc(var(--spacing-unit) * 8)); /* Limit height to viewport minus padding */
    overflow: visible; /* Allow map to overflow if needed */
  }

  #controls-container, #mortar-controls-container {
    width: 40%; /* Slightly increased from 38% */
    max-height: none; /* Allow controls to scroll naturally */
  }

  /* Ensure map stays visible when scrolling through a long control panel */
  #map, #mortar-map {
    position: sticky;
    top: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* Tablet layout - Map above, controls in two columns */
  #controls-container, #mortar-controls-container {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: calc(var(--spacing-unit) * 4); /* Increased gap on wider screens */
  }

  .control-panel {
    width: 48%;
  }
}

@media (max-width: 767px) {
  /* Mobile layout - Stack everything vertically */
  body {
    padding: calc(var(--spacing-unit) * 2); /* 16px padding */
  }

  h1 {
    font-size: 2em;
    margin-bottom: calc(var(--spacing-unit) * 2);
  }

  .intro {
    margin-bottom: calc(var(--spacing-unit) * 3);
  }

  .collapsible {
    padding: var(--spacing-unit);
    font-size: 0.9em;
  }

  .content {
    padding: var(--spacing-unit);
  }

  #map, #mortar-map {
    height: 350px;
  }

  #controls-container, #mortar-controls-container {
    padding: calc(var(--spacing-unit) * 3);
  }

  section {
    padding-bottom: calc(var(--spacing-unit) * 2);
  }

  .form-group {
    margin-bottom: calc(var(--spacing-unit) * 1.5);
  }

  label {
    margin-bottom: calc(var(--spacing-unit) * 0.5);
  }

  select, input[type="number"], input[type="text"] {
    padding: var(--spacing-unit);
  }
}